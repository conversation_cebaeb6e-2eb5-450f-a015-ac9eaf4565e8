<project name="cordova-plugin-unvired-push-sdk" default="updatesource" basedir="." xmlns:ivy="antlib:org.apache.ivy.ant">
	<property name="dist.dir" value="${basedir}/cordova-plugin-unvired-push-sdk"/>
	<property name="release.num" value="99.99.99"/>
  
  	<target name="package">
		<property environment="env" />
		<mkdir dir="${dist.dir}"/>
		<mkdir dir="cordova-plugin-unvired-push-sdk"/>
		<copy overwrite="true" todir="cordova-plugin-unvired-push-sdk/aar">
			<fileset dir="${basedir}/aar"/>
		</copy>
		<copy overwrite="true" todir="cordova-plugin-unvired-push-sdk/src">
			<fileset dir="${basedir}/src"/>
		</copy>
		<copy overwrite="true" todir="cordova-plugin-unvired-push-sdk/www">
			<fileset dir="${basedir}/www"/>
		</copy>
		<copy file="package.json" tofile="cordova-plugin-unvired-push-sdk/package.json" overwrite="true"/>
		<copy file="plugin.xml" tofile="cordova-plugin-unvired-push-sdk/plugin.xml" overwrite="true"/>
		<copy file="README.md" tofile="cordova-plugin-unvired-push-sdk/README.md" overwrite="true"/>
    </target>

	<target name="updatesource" depends="package" >
		<property environment="env" />
		<echo message="Using package version number : ${release.num}"/>
		<replace file="cordova-plugin-unvired-push-sdk/package.json" token="@PACKAGE_NUMBER@" value='${release.num}'/>
		<replace file="cordova-plugin-unvired-push-sdk/plugin.xml" token="@PACKAGE_NUMBER@" value='${release.num}'/>
    </target>    


</project>