<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="12120" systemVersion="16F73" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="12088"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" reuseIdentifier="EmojiCollectionViewCell" id="gTV-IL-0wX" customClass="EmojiCollectionViewCell" customModule="iOSPhotoEditor" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="71" height="69"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="71" height="69"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="😂" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="HtF-AX-oQb">
                        <rect key="frame" x="3" y="-1.5" width="65" height="72.5"/>
                        <fontDescription key="fontDescription" type="system" pointSize="60"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                </subviews>
            </view>
            <constraints>
                <constraint firstItem="HtF-AX-oQb" firstAttribute="centerY" secondItem="gTV-IL-0wX" secondAttribute="centerY" id="aKq-g2-ZSA"/>
                <constraint firstItem="HtF-AX-oQb" firstAttribute="centerX" secondItem="gTV-IL-0wX" secondAttribute="centerX" id="eLO-Xr-JQg"/>
            </constraints>
            <size key="customSize" width="71" height="69"/>
            <connections>
                <outlet property="emojiLabel" destination="HtF-AX-oQb" id="Q1y-jj-Bup"/>
            </connections>
            <point key="canvasLocation" x="44.5" y="90.5"/>
        </collectionViewCell>
    </objects>
</document>
