//
//  InfoView.swift
//  MyApp
//
//  Created by VENKADESH on 17/06/19.
//

import UIKit

enum ImageMode {
    case externalMode, internalMode
}

class InfoView: UIView, UIGestureRecognizerDelegate {
    @IBOutlet weak var infoScrollView: UIScrollView!
    @IBOutlet weak var dragImgView: UIImageView!
    @IBOutlet weak var bgView: UIView!
//    var panRecongnizerEnabled: Bool = false
    weak var parentVC: PhotoEditorViewController?
    var totalImages = 15
    let btnWidth:CGFloat = 135.0
    let btnHeight:CGFloat = 80.0
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    func setupUI(_ imageMode: ImageMode) {
        backgroundColor = .clear
        
        dragImgView.image = UIImage(named: "drag.png")?.withRenderingMode(.alwaysTemplate)
        dragImgView.tintColor = .blue
        var xValue:CGFloat = 2.0
    
        for i in 1...totalImages {
            let btn = UIButton(type: .custom)
            btn.tag = i
            btn.frame = CGRect(x: xValue, y: 2, width: btnWidth, height: btnHeight)
            var imageName = "e" + "\(i)" + ".png"
            if imageMode == .internalMode {
                imageName = "i" + "\(i)" + ".png"
            }
            if let btnImg = UIImage(named: imageName) {
                btn.setImage(btnImg, for: .normal)
                btn.clipsToBounds = true
                btn.addTarget(self, action: #selector(self.ratingButtonClicked(_:)), for: .touchUpInside)
                infoScrollView.addSubview(btn)
                xValue = btn.frame.maxX
                
                if let btnTag = Int(parentVC?.rating ?? ""), btnTag == i {
                    btn.layer.borderColor = UIColor.yellow.cgColor
                    btn.layer.borderWidth = 4
                }
            }
            else {
                break
            }
        }
        
        infoScrollView.contentSize = CGSize(width: xValue + 2, height: btnHeight + 4)
        infoScrollView.layer.cornerRadius = 10
        infoScrollView.clipsToBounds = true
        infoScrollView.layer.borderColor = UIColor.white.cgColor
        infoScrollView.layer.borderWidth = 2
        infoScrollView.bounces = false
        dragImgView.alpha = 0.5
        bgView.frame = CGRect(x: 5, y: 60, width: frame.width - 10, height: btnHeight + 55)
        updateUIFrames()
        
//        let longPressRecognizer = UILongPressGestureRecognizer(target: self, action: #selector(InfoView.longPressAction(_:)))
//        longPressRecognizer.delegate = self
//        infoScrollView.addGestureRecognizer(longPressRecognizer)
        let panRecognizer = UIPanGestureRecognizer(target: self, action: #selector(InfoView.panAction(_:)))
        panRecognizer.delegate = self
        dragImgView.addGestureRecognizer(panRecognizer)
        
        let tapRecognizer = UITapGestureRecognizer(target: self, action: #selector(InfoView.tapped(_:)))
        self.addGestureRecognizer(tapRecognizer)
        
        NotificationCenter.default.removeObserver(self)
        NotificationCenter.default.addObserver(self, selector: #selector(InfoView.rotated), name: UIDevice.orientationDidChangeNotification, object: nil)
    }
    
    @objc func ratingButtonClicked(_ btn: UIButton) {
        if let btnTag = Int(parentVC?.rating ?? "") {
            let prevBtn = infoScrollView.viewWithTag(btnTag)
            prevBtn?.layer.borderColor = UIColor.clear.cgColor
            prevBtn?.layer.borderWidth = 0
        }
        btn.layer.borderColor = UIColor.yellow.cgColor
        btn.layer.borderWidth = 4
        parentVC?.rating = "\(btn.tag)"
    }
    
    @objc func longPressAction(_ recognizer: UILongPressGestureRecognizer?) {
        if recognizer?.state == .began {
            infoScrollView.isScrollEnabled = false
//            panRecongnizerEnabled = true
        }
        if recognizer?.state == .ended {
            infoScrollView.isScrollEnabled = true
//            panRecongnizerEnabled = false
        }
    }
    
    
    @objc func panAction(_ recognizer: UIPanGestureRecognizer?) {
//        if panRecongnizerEnabled == false {
//            return
//        }
        guard let gestureRecognizer = recognizer else {
            return
        }
        if gestureRecognizer.state == .began || gestureRecognizer.state == .changed {
            dragImgView.alpha = 1
            let translation = gestureRecognizer.translation(in: self)
            gestureRecognizer.view!.superview!.center = CGPoint(x: gestureRecognizer.view!.superview!.center.x + translation.x, y: gestureRecognizer.view!.superview!.center.y + translation.y)
            gestureRecognizer.setTranslation(CGPoint.zero, in: self)
        }
        else {
            dragImgView.alpha = 0.5
        }
    }
    
    @objc func tapped(_ recognizer: UILongPressGestureRecognizer?) {
        self.removeFromSuperview()
    }
    
    @objc func rotated() {
        if !UIDevice.current.orientation.isPortrait && !UIDevice.current.orientation.isLandscape {
            return
        }
        updateUIFrames()
    }
    
    func updateUIFrames() {
        guard let parentView = self.superview else {
            return
        }
        self.frame = CGRect(x: 0, y: 0, width: parentView.frame.width, height: parentView.frame.height)
        
        var bgFrame = bgView.frame
        if bgView.frame.maxY > parentView.frame.height {
            bgFrame.origin.y = parentView.frame.height - bgFrame.height
        }
        bgFrame.size.width = frame.width - 10
        bgFrame.size.height = btnHeight + 55 + 4
        bgView.frame = bgFrame
        infoScrollView.frame = CGRect(x: 0, y: 0, width: bgView.frame.width, height: btnHeight + 4)
        dragImgView.frame = CGRect(x: (bgView.frame.width - 100)/2, y: infoScrollView.frame.maxY + 5, width: 100, height: 50)
    }
    
    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldRecognizeSimultaneouslyWith otherGestureRecognizer: UIGestureRecognizer) -> Bool {
        return true
    }
}
