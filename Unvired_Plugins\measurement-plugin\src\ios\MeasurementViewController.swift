//
//  ViewController.swift
//  NextReality_Tutorial5
//
//  Created by <PERSON><PERSON><PERSON> on 6/15/18.
//  Copyright © 2018 Ambuj Punn. All rights reserved.
//

import UIKit
import SceneKit
import ARKit

// https://stackoverflow.com/questions/21886224/drawing-a-line-between-two-points-using-scenekit
extension SCNGeometry {
    class func lineFrom(vector vector1: SCNVector3, toVector vector2: SCNVector3) -> SCNGeometry {
        let indices: [Int32] = [0, 1]
        
        let source = SCNGeometrySource(vertices: [vector1, vector2])
        let element = SCNGeometryElement(indices: indices, primitiveType: .line)
        
        return SCNGeometry(sources: [source], elements: [element])
    }
}

extension SCNVector3 {
    
    static func positionFromTransform(_ transform: matrix_float4x4) -> SCNVector3 {
        return SCNVector3Make(transform.columns.3.x, transform.columns.3.y, transform.columns.3.z)
    }
    
    func distanceTo(_ vector2: SCNVector3) -> Float {
        let dx = vector2.x - self.x
        let dy = vector2.y - self.y
        let dz = vector2.z - self.z
        let distance = Float(sqrt(dx * dx + dy * dy + dz * dz))
        if (distance < 0){
            return (distance * -1)
        } else {
            return (distance)
        }
    }
}

extension Float {
    func metersToInches() -> Float {
        return self * 39.3701
    }
    func metersToCentimeter() -> Float {
        return self * 100
    }
    func meterToFeet() -> Float {
        return self * 3.2808
    }
    func meterToKm() -> Float {
        return self / 1000
    }
}

extension ARSCNView {
    func realWorldVector(screenPos: CGPoint) -> SCNVector3? {
        let planeTestResults = self.hitTest(screenPos, types: [.featurePoint])
        if let result = planeTestResults.first {
            return SCNVector3.positionFromTransform(result.worldTransform)
        }
        
        return nil
    }
}

extension SCNNode {
 
    func normalizeVector(_ iv: SCNVector3) -> SCNVector3 {
        let length = sqrt(iv.x * iv.x + iv.y * iv.y + iv.z * iv.z)
        if length == 0 {
            return SCNVector3(0.0, 0.0, 0.0)
        }
        
        return SCNVector3( iv.x / length, iv.y / length, iv.z / length)
        
    }
    
    func buildLineInTwoPointsWithRotation(from startPoint: SCNVector3,
                                          to endPoint: SCNVector3,
                                          radius: CGFloat,
                                          color: UIColor) -> SCNNode {
        let w = SCNVector3(x: endPoint.x-startPoint.x,
                           y: endPoint.y-startPoint.y,
                           z: endPoint.z-startPoint.z)
        let l = CGFloat(sqrt(w.x * w.x + w.y * w.y + w.z * w.z))
        
        if l == 0.0 {
            // two points together.
            let sphere = SCNSphere(radius: radius)
            sphere.firstMaterial?.diffuse.contents = color
            sphere.firstMaterial?.locksAmbientWithDiffuse = true
            self.geometry = sphere
            self.position = startPoint
            return self
            
        }
        
        let cyl = SCNCylinder(radius: radius, height: l)
        cyl.firstMaterial?.diffuse.contents = color
        cyl.firstMaterial?.locksAmbientWithDiffuse = true
        self.geometry = cyl
        
        //original vector of cylinder above 0,0,0
        let ov = SCNVector3(0, l/2.0,0)
        //target vector, in new coordination
        let nv = SCNVector3((endPoint.x - startPoint.x)/2.0, (endPoint.y - startPoint.y)/2.0,
                            (endPoint.z-startPoint.z)/2.0)
        
        // axis between two vector
        let av = SCNVector3( (ov.x + nv.x)/2.0, (ov.y+nv.y)/2.0, (ov.z+nv.z)/2.0)
        
        //normalized axis vector
        let av_normalized = normalizeVector(av)
        let q0 = Float(0.0) //cos(angel/2), angle is always 180 or M_PI
        let q1 = Float(av_normalized.x) // x' * sin(angle/2)
        let q2 = Float(av_normalized.y) // y' * sin(angle/2)
        let q3 = Float(av_normalized.z) // z' * sin(angle/2)
        
        let r_m11 = q0 * q0 + q1 * q1 - q2 * q2 - q3 * q3
        let r_m12 = 2 * q1 * q2 + 2 * q0 * q3
        let r_m13 = 2 * q1 * q3 - 2 * q0 * q2
        let r_m21 = 2 * q1 * q2 - 2 * q0 * q3
        let r_m22 = q0 * q0 - q1 * q1 + q2 * q2 - q3 * q3
        let r_m23 = 2 * q2 * q3 + 2 * q0 * q1
        let r_m31 = 2 * q1 * q3 + 2 * q0 * q2
        let r_m32 = 2 * q2 * q3 - 2 * q0 * q1
        let r_m33 = q0 * q0 - q1 * q1 - q2 * q2 + q3 * q3
        
        self.transform.m11 = r_m11
        self.transform.m12 = r_m12
        self.transform.m13 = r_m13
        self.transform.m14 = 0.0
        
        self.transform.m21 = r_m21
        self.transform.m22 = r_m22
        self.transform.m23 = r_m23
        self.transform.m24 = 0.0
        
        self.transform.m31 = r_m31
        self.transform.m32 = r_m32
        self.transform.m33 = r_m33
        self.transform.m34 = 0.0
        
        self.transform.m41 = (startPoint.x + endPoint.x) / 2.0
        self.transform.m42 = (startPoint.y + endPoint.y) / 2.0
        self.transform.m43 = (startPoint.z + endPoint.z) / 2.0
        self.transform.m44 = 1.0
        return self
    }
}

enum MeasurementMode {
    case tapMode, dragMode
}

enum UnitMode {
    case centimeter, inch, feet, meter, kilometer
}

class SphereNode: SCNNode {
    init(position: SCNVector3, color: UIColor) {
        super.init()
        let sphereGeometry = SCNSphere(radius: 0.002)
        let material = SCNMaterial()
        material.diffuse.contents = color
        material.lightingModel = .physicallyBased
        sphereGeometry.materials = [material]
        self.geometry = sphereGeometry
        self.position = position
    }
    
    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
}

@objc protocol MeasurementDelegate: NSObjectProtocol {
    func measurementValue(_ value: String)
}

@objc class MeasurementViewController: UIViewController, ARSCNViewDelegate {

//    @IBOutlet var sceneView: ARSCNView!
    
    @objc static func topMostController() -> UIViewController? {
        guard let window = UIApplication.shared.keyWindow, let rootViewController = window.rootViewController else {
            return nil
        }
        var topController = rootViewController
        while let newTopController = topController.presentedViewController {
            topController = newTopController
        }
        return topController
    }
    
    lazy var sceneView: ARSCNView = {
        let view = ARSCNView(frame: CGRect.zero)
        view.delegate = self
        view.autoenablesDefaultLighting = true
        view.antialiasingMode = SCNAntialiasingMode.multisampling4X
        view.debugOptions = [ARSCNDebugOptions.showFeaturePoints]
        return view
    }()
    
    var numberOfTaps = 0
    var lineNode:SCNNode?
    var startPoint: SCNVector3?
    var endPoint: SCNVector3?
    var lbl:UILabel!
    var doneBtn: UIButton!
    var clearBtn: UIButton!
    var addBtn: UIButton!
    var unitBtn: UIButton!
    var plusLbl:UILabel!
    var measuring = false
    
    var mode:MeasurementMode = .dragMode
    var selectedUnit: UnitMode = .centimeter
    
    var currentMeasurement = ""
    
    @objc weak var vcDelegate: MeasurementDelegate?
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        view.addSubview(sceneView)
        
        // Create a new scene
        let scene = SCNScene()

        // Set the scene to the view
        sceneView.scene = scene
        
        let gestureRecognizer = UITapGestureRecognizer(target: self, action: #selector(tapped))
        sceneView.addGestureRecognizer(gestureRecognizer)
        
        lbl = UILabel(frame: CGRect(x: 0, y: 40, width: 60, height: 30))
        lbl.textAlignment = .center
        lbl.font = UIFont.boldSystemFont(ofSize: 15)
        lbl.backgroundColor = UIColor(white: 1, alpha: 0.5)
        lbl.layer.cornerRadius = 5
        lbl.layer.masksToBounds = true
        lbl.isUserInteractionEnabled = true
        view.addSubview(lbl)
        
        plusLbl = UILabel(frame: CGRect(x: 0, y: 0, width: 60, height: 60))
        plusLbl.textAlignment = .center
        plusLbl.text = "+"
        plusLbl.textColor = .red
        plusLbl.font = UIFont.systemFont(ofSize: 50)
        view.addSubview(plusLbl)
        
        let modeBtn = UIButton(type: .system)
        modeBtn.setTitle("♽", for: .normal)
        modeBtn.titleLabel?.font = UIFont.boldSystemFont(ofSize: 25)
        modeBtn.addTarget(self, action: #selector(modeButtonClicked), for: .touchUpInside)
        modeBtn.sizeToFit()
        modeBtn.setTitleColor(.black, for: .normal)
        modeBtn.frame = CGRect(x: 10, y: 20, width: 30, height: 30)
        modeBtn.backgroundColor = UIColor(white: 1, alpha: 1)
        modeBtn.layer.cornerRadius = 5
        modeBtn.layer.masksToBounds = true
        modeBtn.layer.borderWidth = 1
        modeBtn.layer.borderColor = UIColor.black.cgColor
        view.addSubview(modeBtn)
        modeBtn.isHidden = true
        
        unitBtn = UIButton(type: .system)
        unitBtn.setTitle("cm", for: .normal)
        unitBtn.titleLabel?.font = UIFont.systemFont(ofSize: 15)
        unitBtn.addTarget(self, action: #selector(untiButtonClicked), for: .touchUpInside)
        unitBtn.sizeToFit()
        unitBtn.setTitleColor(.black, for: .normal)
        unitBtn.frame = CGRect(x: 10, y: view.frame.height - 40, width: 40, height: 30)
        unitBtn.backgroundColor = UIColor(white: 1, alpha: 1)
        unitBtn.layer.cornerRadius = 5
        unitBtn.layer.masksToBounds = true
        unitBtn.layer.borderWidth = 1
        unitBtn.layer.borderColor = UIColor.black.cgColor
        view.addSubview(unitBtn)
        unitBtn.isHidden = true
        
        doneBtn = UIButton(type: .system)
        doneBtn.setTitle("Done", for: .normal)
        doneBtn.titleLabel?.font = UIFont.boldSystemFont(ofSize: 15)
        doneBtn.addTarget(self, action: #selector(doneButtonClicked), for: .touchUpInside)
        doneBtn.sizeToFit()
        doneBtn.setTitleColor(.black, for: .normal)
        doneBtn.frame = CGRect(x: view.frame.width - 10 - doneBtn.frame.width - 20, y: 20, width: doneBtn.frame.width + 20, height: 30)
        doneBtn.backgroundColor = UIColor(white: 1, alpha: 1)
        doneBtn.layer.cornerRadius = 5
        doneBtn.layer.masksToBounds = true
        doneBtn.layer.borderWidth = 1
        doneBtn.layer.borderColor = UIColor.black.cgColor
        view.addSubview(doneBtn)
        
        clearBtn = UIButton(type: .system)
        clearBtn.setTitle("Clear", for: .normal)
        clearBtn.titleLabel?.font = UIFont.boldSystemFont(ofSize: 15)
        clearBtn.addTarget(self, action: #selector(clearButtonClicked), for: .touchUpInside)
        clearBtn.sizeToFit()
        clearBtn.setTitleColor(.black, for: .normal)
        clearBtn.frame = CGRect(x: view.frame.width - 10 - clearBtn.frame.width - 20, y: view.frame.height - 40, width: clearBtn.frame.width + 20, height: 30)
        clearBtn.backgroundColor = UIColor(white: 1, alpha: 1)
        clearBtn.layer.cornerRadius = 5
        clearBtn.layer.borderWidth = 1
        clearBtn.layer.borderColor = UIColor.black.cgColor
        clearBtn.layer.masksToBounds = true
        view.addSubview(clearBtn)
        
        
        addBtn = UIButton(type: .system)
        addBtn.setTitle("+", for: .normal)
        addBtn.titleLabel?.font = UIFont.systemFont(ofSize: 35)
        addBtn.addTarget(self, action: #selector(addButtonClicked), for: .touchUpInside)
        addBtn.setTitleColor(.black, for: .normal)
        addBtn.frame = CGRect(x: ((view.frame.width - 30)/2)+15, y: view.frame.height - 60, width: 50, height: 50)
        addBtn.backgroundColor = UIColor(white: 1, alpha: 1)
        addBtn.layer.cornerRadius = 25
        addBtn.layer.masksToBounds = true
        addBtn.layer.borderWidth = 1
        addBtn.layer.borderColor = UIColor.black.cgColor
        view.addSubview(addBtn)
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        sceneView.frame = view.bounds
        lbl.sizeToFit()
        lbl.frame.size = CGSize(width: lbl.frame.width + 30, height: 30)
        if let txt = lbl.text, txt.count == 0 {
            lbl.frame.size = CGSize(width: 0, height: 0)
        }
        lbl.center = CGPoint(x: view.center.x, y: lbl.center.y)
        plusLbl.frame = CGRect(x: 0, y: 0, width: 60, height: 60)
        plusLbl.center = view.center
        doneBtn.frame = CGRect(x: view.frame.width - 10 - 80, y: 40, width: 80, height: 30)
        clearBtn.frame = CGRect(x: view.frame.width - 10 - 80, y: view.frame.height - 40 - 10, width: 80, height: 30)
        addBtn.frame = CGRect(x: (view.frame.width - 50)/2, y: view.frame.height - 60, width: 50, height: 50)
        unitBtn.frame = CGRect(x: 10, y: clearBtn.frame.minY, width: 40, height: 30)
        addBtn.isHidden = mode == .tapMode
        plusLbl.isHidden = mode == .tapMode
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        let configuration = ARWorldTrackingConfiguration()
        configuration.isLightEstimationEnabled = true
        configuration.planeDetection = .horizontal
        sceneView.session.run(configuration, options: [.resetTracking, .removeExistingAnchors])
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        // Pause the view's session
        sceneView.session.pause()
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
//        let alert = UIAlertController(title: "Move device to use", message: nil, preferredStyle: .alert)
//        alert.addAction(UIAlertAction(title: "OK", style: .default, handler: nil))
//        self.present(alert, animated: true, completion: nil)
    }
    
    override func didReceiveMemoryWarning() {
        super.didReceiveMemoryWarning()
        // Release any cached data, images, etc that aren't in use.
    }
    
    @objc func untiButtonClicked() {
        switch selectedUnit {
        case .centimeter:
            selectedUnit = .inch
        case .inch:
            selectedUnit = .feet
        case .feet:
            selectedUnit = .meter
        case .meter:
            selectedUnit = .kilometer
        case .kilometer:
            selectedUnit = .centimeter
        }
        calculateDistanceAndDisplay()
    }
    
    @objc func modeButtonClicked() {
        if mode == .tapMode {
            mode = .dragMode
        }
        else {
            mode = .tapMode
        }
        addBtn.isHidden = mode == .tapMode
        plusLbl.isHidden = mode == .tapMode
    }
    
    @objc func addButtonClicked() {
        if plusLbl.textColor == .red {
            return
        }
        if (numberOfTaps == 0) {
            clearButtonClicked()
        }
        numberOfTaps += 1
        if numberOfTaps == 1 {
            measuring = true
        }
        else {
            numberOfTaps = 0
            measuring = false
            if(endPoint != nil) {
                addMarker(position: endPoint!, color: .white)
            }
        }
    }
    
    func calculateDistanceAndDisplay() {
        
        var unitText = ""
        switch selectedUnit {
        case .centimeter:
            unitText = "cm"
        case .inch:
            unitText = "in"
        case .feet:
            unitText = "ft"
        case .meter:
            unitText = "m"
        case .kilometer:
            unitText = "km"
        }
        unitBtn.setTitle(unitText, for: .normal)
        
        if startPoint == nil || endPoint == nil {
            return
        }
        
        var distance:Float = 0.0
        switch selectedUnit {
        case .centimeter:
            distance = startPoint!.distanceTo(endPoint!).metersToCentimeter()
        case .inch:
            distance = startPoint!.distanceTo(endPoint!).metersToInches()
        case .feet:
            distance = startPoint!.distanceTo(endPoint!).meterToFeet()
        case .meter:
            distance = startPoint!.distanceTo(endPoint!)
        case .kilometer:
            distance = startPoint!.distanceTo(endPoint!).meterToKm()
        }
        currentMeasurement = String(format: "%.1f", distance)
        lbl.text = String(format: "%.1f %@", distance, unitText)
        if distance < 100 {
            currentMeasurement = String(format: "%.2f", distance)
            lbl.text = String(format: "%.2f %@", distance, unitText)
        }
        lbl.sizeToFit()
    }
    
    func session(_ session: ARSession, cameraDidChangeTrackingState camera: ARCamera) {
        clearButtonClicked()
        var status = "Loading..."
        switch camera.trackingState {
        case ARCamera.TrackingState.notAvailable:
            status = "Not available"
        case ARCamera.TrackingState.limited(_):
            status = "Analyzing..."
        case ARCamera.TrackingState.normal:
            status = "Ready"
        }
        lbl.text = status
        currentMeasurement = ""
        lbl.sizeToFit()
    }
    
    func session(_ session: ARSession, didFailWithError error: Error) {
        // Present an error message to the user
        print("SESSION FAILD WITH ERROR")
    }
    
    func sessionWasInterrupted(_ session: ARSession) {
        // Inform the user that the session has been interrupted, for example, by presenting an overlay
        print("SESSION INTERRUPTED")
    }
    
    func sessionInterruptionEnded(_ session: ARSession) {
        print("SESSION INTERRUPTION ENDED")
        // Reset tracking and/or remove existing anchors if consistent tracking is required
        
    }
    
    func renderer(_ renderer: SCNSceneRenderer, updateAtTime time: TimeInterval) {
        DispatchQueue.main.async {
            self.detectObjects()
        }
    }
    
    func detectObjects() {
        plusLbl.textColor = .red
        if let worldPos = sceneView.realWorldVector(screenPos: view.center) {
            plusLbl.textColor = .green
            if measuring && mode == .dragMode {
                if startPoint == nil {
                    startPoint = worldPos
                    lbl.text = ""
                    currentMeasurement = ""
                    lbl.sizeToFit()
                    addMarker(position: startPoint!, color: .white)
                }
                endPoint = worldPos
                lineNode?.removeFromParentNode()
                lineNode = SCNNode().buildLineInTwoPointsWithRotation(from: startPoint!, to: endPoint!, radius: 0.0005, color: .white)
                sceneView.scene.rootNode.addChildNode(lineNode!)
                calculateDistanceAndDisplay()
            }
        }
    }
    
    @objc func doneButtonClicked() {
        vcDelegate?.measurementValue(currentMeasurement)
        self.dismiss(animated: true, completion: nil)
    }
    
    @objc func clearButtonClicked() {
        
        measuring = false
        lbl.text = ""
        currentMeasurement = ""
        lbl.sizeToFit()
        numberOfTaps = 0
        startPoint = nil
        endPoint = nil
        if plusLbl.textColor == .green {
            sceneView.scene.rootNode.enumerateChildNodes { (node, stop) in
                node.removeFromParentNode()
            }
        }
    }
    
    @objc func tapped(gesture: UITapGestureRecognizer) {
        
        if mode == .dragMode {
            return
        }
        
        // Get 2D position of touch event on screen
        let touchPosition = gesture.location(in: sceneView)
        
        // Translate those 2D points to 3D points using hitTest (existing plane)
        let hitTestResults = sceneView.hitTest(touchPosition, types: .featurePoint)
        
        guard let hitTest = hitTestResults.first else {
            return
        }

        if (numberOfTaps == 0) {
            clearButtonClicked()
        }
        
        numberOfTaps += 1
        // If first tap, add red marker. If second tap, add green marker and reset to 0
        if numberOfTaps == 1 {
            lbl.text = ""
            currentMeasurement = ""
            lbl.sizeToFit()
            addRedMarker(hitTestResult: hitTest)
            startPoint = SCNVector3(hitTest.worldTransform.columns.3.x, hitTest.worldTransform.columns.3.y, hitTest.worldTransform.columns.3.z)
        }
        else {
            // After 2nd tap, reset taps to 0
            numberOfTaps = 0
            addGreenMarker(hitTestResult: hitTest)
            endPoint = SCNVector3(hitTest.worldTransform.columns.3.x, hitTest.worldTransform.columns.3.y, hitTest.worldTransform.columns.3.z)
            if startPoint != nil && endPoint != nil {
                addLineBetween(start: startPoint!, end: endPoint!)
                calculateDistanceAndDisplay()
            }
//            addDistanceText(distance: SCNVector3.distanceFrom(vector: startPoint, toVector: endPoint), at: endPoint)
        }
    }
    
    func addRedMarker(hitTestResult: ARHitTestResult) {
        addMarker(hitTestResult: hitTestResult, color: .white) //.red
    }
    
    func addGreenMarker(hitTestResult: ARHitTestResult) {
        addMarker(hitTestResult: hitTestResult, color: .white) //.green
    }
    
    func addMarker(hitTestResult: ARHitTestResult, color: UIColor) {
        let position = SCNVector3(hitTestResult.worldTransform.columns.3.x, hitTestResult.worldTransform.columns.3.y, hitTestResult.worldTransform.columns.3.z)
        let sphere = SphereNode(position: position, color: color)
        sceneView.scene.rootNode.addChildNode(sphere)
    }
   
    func addMarker(position: SCNVector3, color: UIColor) {
        let sphere = SphereNode(position: position, color: color)
        sceneView.scene.rootNode.addChildNode(sphere)
    }
    
    func addLineBetween(start: SCNVector3, end: SCNVector3) {
        
//        let lineGeometry = SCNGeometry.lineFrom(vector: start, toVector: end)
//        let lineNode = SCNNode(geometry: lineGeometry)
//        sceneView.scene.rootNode.addChildNode(lineNode)
        
        let lineNode = SCNNode()
        sceneView.scene.rootNode.addChildNode(lineNode.buildLineInTwoPointsWithRotation(from: start, to: end, radius: 0.0005, color: .white))
    }
    
    func addDistanceText(distance: Float, at point: SCNVector3) {
        let textGeometry = SCNText(string: String(format: "%.1f cm", distance.metersToCentimeter()), extrusionDepth: 1)
        textGeometry.font = UIFont.systemFont(ofSize: 5)
        textGeometry.flatness = 0.05
        textGeometry.firstMaterial?.diffuse.contents = UIColor.black

        let textNode = SCNNode(geometry: textGeometry)
        textNode.position = SCNVector3Make(point.x, point.y, point.z);
        textNode.scale = SCNVector3Make(0.002, 0.002, 0.002)
        sceneView.scene.rootNode.addChildNode(textNode)
    }
}
