//
//  ViewController.swift
//  NextReality_Tutorial5
//
//  Created by <PERSON><PERSON><PERSON> on 6/15/18.
//  Copyright © 2018 Ambuj Punn. All rights reserved.
//

import UIKit
import ARKit

@objc(Measurement) class Measurement: CDVPlugin, MeasurementDelegate {

    var callbackId: String = ""

    @objc func isARSupported(_ command: CDVInvokedUrlCommand) {
        let pluginResult = CDVPluginResult(status: CDVCommandStatus_OK, messageAs: ARConfiguration.isSupported)
        self.commandDelegate!.send(pluginResult, callbackId: command.callbackId)
    }
    
    @objc func takeMeasurement(_ command: CDVInvokedUrlCommand) {
        self.callbackId = command.callbackId;
        if !ARConfiguration.isSupported {
            let pluginResult = CDVPluginResult(status: CDVCommandStatus_ERROR, messageAs: "Augmented reality does not supported in this device.")
            self.commandDelegate!.send(pluginResult, callbackId: command.callbackId)
            return;
        }
        guard let topVC = MeasurementViewController.topMostController() else {
            return;
        }
        var measurementMode:UnitMode = .meter
        if let dataDict = command.arguments.first as? [String: String], let mode = dataDict["UOM"]  {
            switch mode {
            case "cm":
                measurementMode = .centimeter
            case "in":
                measurementMode = .inch
            case "ft":
                measurementMode = .feet
            case "m":
                measurementMode = .meter
            case "km":
                measurementMode = .kilometer
            default:
                measurementMode = .centimeter
            }
        }
        let vc = MeasurementViewController()
        vc.vcDelegate = self
        vc.selectedUnit = measurementMode
        vc.modalPresentationStyle = .fullScreen
        topVC.present(vc, animated: true, completion: nil)
    }

    func measurementValue(_ value: String) {
        let pluginResult = CDVPluginResult(status: CDVCommandStatus_OK, messageAs: value)
        self.commandDelegate!.send(pluginResult, callbackId: callbackId)
    }
}
