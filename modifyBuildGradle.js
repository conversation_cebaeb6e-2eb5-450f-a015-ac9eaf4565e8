const fs = require('fs');
const path = require('path');

// List of plugin build.gradle files that might contain Firebase Crashlytics plugin
const pluginGradleFiles = [
    path.join(__dirname, 'platforms', 'android', 'cordova-plugin-firebase-crash', 'inspections-build.gradle'),
    path.join(__dirname, 'platforms', 'android', 'cordova-plugin-firebasex', 'inspections-build.gradle')
];

const pluginLine = "apply plugin: com.google.firebase.crashlytics.buildtools.gradle.CrashlyticsPlugin";
const conditionalPlugin = `// Apply Crashlytics plugin conditionally to avoid conflicts with other Firebase plugins
if (!plugins.hasPlugin('com.google.firebase.crashlytics')) {
    apply plugin: com.google.firebase.crashlytics.buildtools.gradle.CrashlyticsPlugin
}`;

function fixCrashlyticsInFile(filePath) {
    if (!fs.existsSync(filePath)) {
        console.log(`File does not exist: ${filePath}`);
        return;
    }

    fs.readFile(filePath, 'utf8', (err, data) => {
        if (err) {
            console.error(`Could not read ${filePath}:`, err);
            return;
        }

        let modified = data.replace(
            new RegExp(pluginLine, 'g'),
            conditionalPlugin
        );

        if (modified !== data) {
            fs.writeFile(filePath, modified, 'utf8', err => {
                if (err) {
                    console.error(`Could not write ${filePath}:`, err);
                    return;
                }
                console.log(`${filePath} updated to prevent duplicate Crashlytics plugin application.`);
            });
        } else {
            console.log(`No Crashlytics plugin line found to modify in ${filePath}.`);
        }
    });
}

// Process all plugin gradle files
pluginGradleFiles.forEach(fixCrashlyticsInFile);