const fs = require('fs');
const path = require('path');

const gradlePath = path.join(__dirname, 'platforms', 'android', 'app', 'build.gradle');
const pluginLine = "apply plugin: com.google.firebase.crashlytics.buildtools.gradle.CrashlyticsPlugin";
const conditionalPlugin = `if (!plugins.hasPlugin('com.google.firebase.crashlytics')) {
    apply plugin: com.google.firebase.crashlytics.buildtools.gradle.CrashlyticsPlugin
}`;

fs.readFile(gradlePath, 'utf8', (err, data) => {
    if (err) {
        console.error('Could not read build.gradle:', err);
        process.exit(1);
    }

    let modified = data.replace(
        new RegExp(pluginLine, 'g'),
        `// ${pluginLine} (commented by fix-crashlytics.js)\n${conditionalPlugin}`
    );

    if (modified !== data) {
        fs.writeFile(gradlePath, modified, 'utf8', err => {
            if (err) {
                console.error('Could not write build.gradle:', err);
                process.exit(1);
            }
            console.log('build.gradle updated to prevent duplicate Crashlytics plugin application.');
        });
    } else {
        console.log('No Crashlytics plugin line found to modify.');
    }
});