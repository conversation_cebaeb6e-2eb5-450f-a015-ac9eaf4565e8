repositories {
    // Required for build
    jcenter()
    flatDir {
        dirs 'src/main/libs'
    }
}

android {
    compileSdkVersion 28
   
    defaultConfig {
        minSdkVersion 16
        targetSdkVersion 28        
    }
    
    packagingOptions {
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/LICENSE'
  }

}

dependencies {
    implementation fileTree(dir: 'src/main/libs', include: ['*.aar'])
}