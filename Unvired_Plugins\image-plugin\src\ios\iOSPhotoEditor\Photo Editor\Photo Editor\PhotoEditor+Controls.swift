//
//  PhotoEditor+Controls.swift
//  Pods
//
//  Created by <PERSON> on 6/16/17.
//
//

import Foundation
import UIKit

// MARK: - Control
public enum control {
    case crop
    case sticker
    case draw
    case text
    case save
    case share
    case clear
    case undo
}

extension PhotoEditorViewController {

     //MARK: Top Toolbar
    
    @IBAction func cancelButtonTapped(_ sender: Any) {
        photoEditorDelegate?.canceledEditing()
        self.dismiss(animated: true, completion: nil)
    }

    @IBAction func cropButtonTapped(_ sender: UIButton) {
        let controller = CropViewController()
        controller.delegate = self
        controller.image = image
        let navController = UINavigationController(rootViewController: controller)
        present(navController, animated: true, completion: nil)
    }

    @IBAction func stickersButtonTapped(_ sender: Any) {
        addStickersViewController()
    }

    @IBAction func drawButtonTapped(_ sender: Any) {
        isDrawing = true
        canvasImageView.isUserInteractionEnabled = false
        doneButton.isHidden = false
        colorPickerView.isHidden = false
        hideToolbar(hide: true)
        undoObject = canvasView.toImage()
        undoButton.isEnabled = true
    }

    @IBAction func textButtonTapped(_ sender: Any) {
        isTyping = true
        let textView = UITextView(frame: CGRect(x: 0, y: canvasImageView.center.y,
                                                width: UIScreen.main.bounds.width, height: 30))
        textView.tag = self.addedTexts.count
        textView.textAlignment = .center
        textView.font = UIFont(name: "Helvetica", size: 30)
        textView.textColor = textColor
        textView.layer.shadowColor = UIColor.black.cgColor
        textView.layer.shadowOffset = CGSize(width: 1.0, height: 0.0)
        textView.layer.shadowOpacity = 0.2
        textView.layer.shadowRadius = 1.0
        textView.layer.backgroundColor = UIColor.clear.cgColor
        textView.autocorrectionType = .no
        textView.isScrollEnabled = false
        textView.delegate = self
        self.canvasImageView.addSubview(textView)
        addGestures(view: textView)
        textView.becomeFirstResponder()
    }    
    
    @IBAction func doneButtonTapped(_ sender: Any) {
        view.endEditing(true)
        doneButton.isHidden = true
        colorPickerView.isHidden = true
        canvasImageView.isUserInteractionEnabled = true
        hideToolbar(hide: false)
        isDrawing = false
    }
    
    @IBAction func infoButtonTapped(_ sender: UIButton) {
        guard let views = Bundle(for: InfoView.self).loadNibNamed("InfoView", owner: self, options: nil), let infoView = views.first as? InfoView else {
            return
        }
        view.addSubview(infoView)
        infoView.parentVC = self
        infoView.setupUI(imageMode)
    }
    
    //MARK: Bottom Toolbar
    
    @IBAction func saveButtonTapped(_ sender: AnyObject) {
        UIImageWriteToSavedPhotosAlbum(canvasView.toImage(),self, #selector(PhotoEditorViewController.image(_:didFinishSavingWithError:contextInfo:)), nil)
    }
    
    @IBAction func shareButtonTapped(_ sender: UIButton) {
        let activity = UIActivityViewController(activityItems: [canvasView.toImage()], applicationActivities: nil)
        present(activity, animated: true, completion: nil)
    }
    
    @IBAction func clearButtonTapped(_ sender: AnyObject) {
        
        if self.canvasImageView.image == nil {
            return
        }
        
        let alertController = UIAlertController(title: "Confirmation", message: "Are you sure you want to reset the changes?", preferredStyle: .alert)
        alertController.addAction(UIAlertAction(title: "OK", style: .default, handler: { (action) in
            //clear texts
            self.addedTexts = []
            self.availableTextIndex = []
            self.undoObject = nil
            //clear drawing
            self.canvasImageView.image = nil
            //clear stickers and textviews
            for subview in self.canvasImageView.subviews {
                subview.removeFromSuperview()
            }
        }))
        alertController.addAction(UIAlertAction(title: "Cancel", style: .cancel, handler:nil))
        self.present(alertController, animated: true, completion: nil)
    }
    
    @IBAction func continueButtonPressed(_ sender: Any) {
        let img = self.canvasView.toImage()
        var latitude = ""
        var longitude = ""
        if let loc = locationManager.location {
            latitude = "\(loc.coordinate.latitude)"
            longitude = "\(loc.coordinate.longitude)"
        }
        let infoDict: [String: Any] = [
            "texts" : addedTexts.joined(separator: ", "),
            "rating": rating,
            "latitude": latitude,
            "longitude": longitude
        ]
        
        photoEditorDelegate?.doneEditing(image: imageView.image!, editedImage: img, additionalInfo: infoDict)
        self.dismiss(animated: true, completion: nil)
    }

    @IBAction func undoButtonTapped(_ sender: UIButton) {
        if let img = undoObject as? UIImage {
            canvasImageView.image = img
            undoButton.isEnabled = false
        }
        else if let tv = undoObject as? UITextView {
            tv.removeFromSuperview()
            undoButton.isEnabled = false
        }
    }
    
    //MAKR: helper methods
    
    @objc func image(_ image: UIImage, didFinishSavingWithError error: NSError?, contextInfo: UnsafeRawPointer) {
        var title = ""
        var message = ""
        if let error = error {
            title = "Image not saved!"
            message = error.localizedDescription
        } else {
            title = "Image Saved"
            message = "Image successfully saved to Photos library"
        }
        
        let alert = UIAlertController(title: title, message: message, preferredStyle: UIAlertController.Style.alert)
        alert.addAction(UIAlertAction(title: "OK", style: UIAlertAction.Style.default, handler: nil))
        self.present(alert, animated: true, completion: nil)
    }
    
    func hideControls() {
        for control in hiddenControls {
            switch control {
            case .clear:
                clearButton.isHidden = true
            case .crop:
                cropButton.isHidden = true
            case .draw:
                drawButton.isHidden = true
            case .save:
                saveButton.isHidden = true
            case .share:
                shareButton.isHidden = true
            case .sticker:
                stickerButton.isHidden = true
            case .text:
                stickerButton.isHidden = true
            case .undo:
                undoButton.isHidden = true
            }
        }
    }
    
}

extension PhotoEditorViewController: UIPopoverPresentationControllerDelegate {
    
    public func adaptivePresentationStyle(for controller: UIPresentationController, traitCollection: UITraitCollection) -> UIModalPresentationStyle {
        return .none
    }
    
    public func popoverPresentationControllerShouldDismissPopover(_ popoverPresentationController: UIPopoverPresentationController) -> Bool {
        self.dismiss(animated: true) { () -> Void in
        }
        return true
    }
}
