<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="12120" systemVersion="16F73" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="12088"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" reuseIdentifier="StickerCollectionViewCell" id="gTV-IL-0wX" customClass="StickerCollectionViewCell" customModule="iOSPhotoEditor" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="139" height="81"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="139" height="81"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <imageView userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="KOC-sF-DQ7">
                        <rect key="frame" x="0.0" y="0.0" width="139" height="81"/>
                    </imageView>
                </subviews>
            </view>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="calibratedWhite"/>
            <constraints>
                <constraint firstItem="KOC-sF-DQ7" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="2yY-EC-OBG"/>
                <constraint firstAttribute="trailing" secondItem="KOC-sF-DQ7" secondAttribute="trailing" id="IjG-hv-eoa"/>
                <constraint firstAttribute="bottom" secondItem="KOC-sF-DQ7" secondAttribute="bottom" id="LSw-wL-WBM"/>
                <constraint firstItem="KOC-sF-DQ7" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="Zq3-3X-uWQ"/>
            </constraints>
            <size key="customSize" width="139" height="81"/>
            <connections>
                <outlet property="stickerImage" destination="KOC-sF-DQ7" id="qYt-m0-cB8"/>
            </connections>
            <point key="canvasLocation" x="78.5" y="76.5"/>
        </collectionViewCell>
    </objects>
</document>
