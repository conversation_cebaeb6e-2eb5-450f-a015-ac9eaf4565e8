<?xml version="1.0" encoding="UTF-8"?>
<plugin xmlns="http://apache.org/cordova/ns/plugins/1.0" xmlns:android="http://schemas.android.com/apk/res/android" id="cordova-plugin-measurement" version="0.1.3">
   <name>Measurement</name>
   <js-module name="Measurement" src="www/Measurement.js">
      <clobbers target="measurement" />
   </js-module>
   <platform name="ios">
      <preference name="CAMERA_USAGE_DESCRIPTION" default="App needs access to camera for taking measurement."/>
      <config-file target="*-Info.plist" parent="NSCameraUsageDescription">
          <string>$CAMERA_USAGE_DESCRIPTION</string>
      </config-file>
      <config-file parent="/*" target="config.xml">
         <feature name="Measurement">
            <param name="ios-package" value="Measurement" />
         </feature>
      </config-file>
      <source-file src="src/ios/Measurement.swift" />
      <source-file src="src/ios/MeasurementViewController.swift" />
   </platform>
   
   <platform name="android">
      <config-file target="config.xml" parent="/*">
         <feature name="Measurement">
            <param name="android-package" value="com.unvired.measurement.plugin.AndroidMeasurementPlugin"/>
         </feature>
      </config-file>
      
        <config-file target="AndroidManifest.xml" parent="/manifest/application">
            <activity android:label="MeasureActivity" android:name="com.unvired.measurement.activity.MeasureActivity" android:theme="@style/Theme.AppCompat.Light">
            </activity>
            <meta-data android:name="com.google.ar.core" android:value="optional" />
        </config-file>


      <!--<lib-file src="*.aar"/>-->
      <framework src='com.google.ar.sceneform.ux:sceneform-ux:1.11.0'/>
      <framework src="src/android/build.gradle" custom="true" type="gradleReference" />

    <resource-file src="aar/unvired_measurement.aar" target="libs/unvired_measurement.aar" />

   </platform>

</plugin>