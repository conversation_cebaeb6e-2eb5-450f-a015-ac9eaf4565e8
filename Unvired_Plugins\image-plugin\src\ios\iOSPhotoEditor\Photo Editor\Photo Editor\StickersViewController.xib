<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="12120" systemVersion="16F73" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="12088"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="StickersViewController" customModule="iOSPhotoEditor" customModuleProvider="target">
            <connections>
                <outlet property="headerView" destination="Jqb-lC-sL5" id="2Bt-iO-hyv"/>
                <outlet property="holdView" destination="caC-0e-fET" id="T8J-gB-ag8"/>
                <outlet property="pageControl" destination="fPm-Rj-wEI" id="wow-uc-U80"/>
                <outlet property="scrollView" destination="389-Pb-g4p" id="EiN-Xe-ZxU"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Jqb-lC-sL5">
                    <rect key="frame" x="0.0" y="0.0" width="375" height="40"/>
                    <subviews>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="caC-0e-fET">
                            <rect key="frame" x="162.5" y="8" width="50" height="5"/>
                            <color key="backgroundColor" red="0.96470588239999999" green="0.96470588239999999" blue="0.96470588239999999" alpha="1" colorSpace="calibratedRGB"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="50" id="GqZ-hh-S9K"/>
                                <constraint firstAttribute="height" constant="5" id="uq0-60-mnE"/>
                            </constraints>
                        </view>
                        <pageControl opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" numberOfPages="3" translatesAutoresizingMaskIntoConstraints="NO" id="fPm-Rj-wEI">
                            <rect key="frame" x="168" y="13" width="39" height="37"/>
                        </pageControl>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="calibratedWhite"/>
                    <constraints>
                        <constraint firstItem="caC-0e-fET" firstAttribute="top" secondItem="Jqb-lC-sL5" secondAttribute="top" constant="8" id="S62-GD-S5G"/>
                        <constraint firstAttribute="height" constant="40" id="Tl2-jg-Adq"/>
                        <constraint firstItem="fPm-Rj-wEI" firstAttribute="centerX" secondItem="Jqb-lC-sL5" secondAttribute="centerX" id="jKc-Hd-3e8"/>
                        <constraint firstItem="fPm-Rj-wEI" firstAttribute="top" secondItem="caC-0e-fET" secondAttribute="bottom" id="oH1-Ez-1Ze"/>
                        <constraint firstItem="caC-0e-fET" firstAttribute="centerX" secondItem="Jqb-lC-sL5" secondAttribute="centerX" id="qvF-zt-xDj"/>
                    </constraints>
                </view>
                <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" directionalLockEnabled="YES" translatesAutoresizingMaskIntoConstraints="NO" id="389-Pb-g4p">
                    <rect key="frame" x="0.0" y="40" width="375" height="627"/>
                </scrollView>
            </subviews>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="calibratedWhite"/>
            <constraints>
                <constraint firstItem="Jqb-lC-sL5" firstAttribute="top" secondItem="i5M-Pr-FkT" secondAttribute="top" id="534-wp-qUu"/>
                <constraint firstAttribute="bottom" secondItem="389-Pb-g4p" secondAttribute="bottom" id="alT-c1-6ub"/>
                <constraint firstItem="389-Pb-g4p" firstAttribute="leading" secondItem="i5M-Pr-FkT" secondAttribute="leading" id="eu1-LX-lYG"/>
                <constraint firstItem="Jqb-lC-sL5" firstAttribute="leading" secondItem="i5M-Pr-FkT" secondAttribute="leading" id="fvh-Gm-lz3"/>
                <constraint firstAttribute="trailing" secondItem="389-Pb-g4p" secondAttribute="trailing" id="gLR-A6-iQ1"/>
                <constraint firstItem="389-Pb-g4p" firstAttribute="top" secondItem="Jqb-lC-sL5" secondAttribute="bottom" id="iQj-xf-VZX"/>
                <constraint firstAttribute="trailing" secondItem="Jqb-lC-sL5" secondAttribute="trailing" id="tYL-lr-Vzj"/>
            </constraints>
            <nil key="simulatedStatusBarMetrics"/>
            <point key="canvasLocation" x="33.5" y="54.5"/>
        </view>
    </objects>
</document>
