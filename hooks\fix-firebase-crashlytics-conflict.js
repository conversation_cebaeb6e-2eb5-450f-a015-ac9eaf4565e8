#!/usr/bin/env node

/**
 * Cordova Hook: Fix Firebase Crashlytics Plugin Conflicts
 * 
 * This hook automatically fixes conflicts between cordova-plugin-firebase-crash
 * and cordova-plugin-firebasex by making the Crashlytics plugin application conditional.
 * 
 * The hook runs before the Android build process and modifies the plugin build.gradle
 * files to prevent duplicate plugin application errors.
 */

const fs = require('fs');
const path = require('path');

module.exports = function(context) {
    console.log('Running Firebase Crashlytics conflict fix hook...');
    
    // Only run for Android platform
    if (context.opts.platforms.indexOf('android') === -1) {
        console.log('Skipping Firebase Crashlytics fix - not building for Android');
        return;
    }

    const platformRoot = path.join(context.opts.projectRoot, 'platforms', 'android');
    
    // List of plugin build.gradle files that might contain Firebase Crashlytics plugin
    const pluginGradleFiles = [
        path.join(platformRoot, 'cordova-plugin-firebase-crash', 'inspections-build.gradle'),
        path.join(platformRoot, 'cordova-plugin-firebasex', 'inspections-build.gradle')
    ];

    const pluginLine = "apply plugin: com.google.firebase.crashlytics.buildtools.gradle.CrashlyticsPlugin";
    const conditionalPlugin = `// Apply Crashlytics plugin conditionally to avoid conflicts with other Firebase plugins
if (!plugins.hasPlugin('com.google.firebase.crashlytics')) {
    apply plugin: com.google.firebase.crashlytics.buildtools.gradle.CrashlyticsPlugin
}`;

    function fixCrashlyticsInFile(filePath) {
        if (!fs.existsSync(filePath)) {
            console.log(`File does not exist: ${filePath}`);
            return;
        }

        try {
            const data = fs.readFileSync(filePath, 'utf8');
            
            // Check if the file already has the conditional check
            if (data.includes("plugins.hasPlugin('com.google.firebase.crashlytics')")) {
                console.log(`${filePath} already has conditional Crashlytics plugin application.`);
                return;
            }
            
            // Replace the direct plugin application with conditional application
            const modified = data.replace(
                new RegExp(pluginLine, 'g'),
                conditionalPlugin
            );

            if (modified !== data) {
                fs.writeFileSync(filePath, modified, 'utf8');
                console.log(`✓ Fixed Firebase Crashlytics conflict in: ${filePath}`);
            } else {
                console.log(`No Crashlytics plugin line found in: ${filePath}`);
            }
        } catch (err) {
            console.error(`Error processing ${filePath}:`, err.message);
        }
    }

    // Process all plugin gradle files
    let filesProcessed = 0;
    pluginGradleFiles.forEach(filePath => {
        if (fs.existsSync(filePath)) {
            fixCrashlyticsInFile(filePath);
            filesProcessed++;
        }
    });

    if (filesProcessed > 0) {
        console.log(`✓ Firebase Crashlytics conflict fix completed. Processed ${filesProcessed} files.`);
    } else {
        console.log('No Firebase plugin build.gradle files found to process.');
    }
};
