<project name="cordova-plugin-unvired-push-sdk" default="publish" basedir="." xmlns:ivy="antlib:org.apache.ivy.ant">
	<property name="dist.dir" value="${basedir}/dist"/>

	 <scriptdef name="substring" language="javascript">
	     <attribute name="text" />
	     <attribute name="start" />
	     <attribute name="end" />
	     <attribute name="property" />
	     <![CDATA[
	       var text = attributes.get("text");
	       var start = attributes.get("start");
	       var end = attributes.get("end") || text.length();
	       project.setProperty(attributes.get("property"), text.substring(start, end));
	     ]]>
  	</scriptdef>

	<scriptdef name="packageversion" language="javascript">
	    <attribute name="text" />
	    <attribute name="start" />
	    <attribute name="end" />
	    <attribute name="property" />
	    <![CDATA[
			var text = attributes.get("text");
	       	var start = attributes.get("start");
	       	var end = attributes.get("end") || text.length();
	       	var newstring = text.substring(start, end);
			newstring = newstring.replace(/\./g,'~');
	       	var split = newstring.split("~");
	       	var first = +split[0];
	       	first = first.toString();
	       	var middle = +split[1];
			middle = middle.toString();
	       	var last = +split[2];
			last = last.toString();
	       	
	       	newstring = first + '.' + middle + '.' + last;
	       	project.setProperty(attributes.get("property"), newstring);
	    ]]>
  	</scriptdef>  

    <!-- Get the release number -->
    <target name="getbuildno">
        <property environment="env" />

        <java jar="/Users/<USER>/Jenkins/BuildNo/BuildNo.jar" fork="true" failonerror="true" maxmemory="128m">
            <arg value="CORDOVA_PLUGIN_UNVIRED_PUSH_SDK"/>
            <arg value="-r=true"/>
            <arg value="-n=true"/>
        </java>

        <!-- Now read into the build numberfile into release.str property -->
        <loadfile property="release.str"
            srcFile="BuildNo.txt" failonerror="true">
        </loadfile>

        <echo message="Using release number : ${release.str}"/>
    </target>

	<target name="package" depends="getbuildno">
		<property environment="env" />

		<mkdir dir="${dist.dir}"/>
		<mkdir dir="cordova-plugin-unvired-push-sdk"/>
		
		<copy overwrite="true" todir="cordova-plugin-unvired-push-sdk/src">
			<fileset dir="${basedir}/src"/>
		</copy>

		<copy overwrite="true" todir="cordova-plugin-unvired-push-sdk/www">
			<fileset dir="${basedir}/www"/>
		</copy>
		
		<copy file="package.json" tofile="cordova-plugin-unvired-push-sdk/package.json" overwrite="true"/>
		<copy file="plugin.xml" tofile="cordova-plugin-unvired-push-sdk/plugin.xml" overwrite="true"/>
		<copy file="README.md" tofile="cordova-plugin-unvired-push-sdk/README.md" overwrite="true"/>
    </target>	

	<target name="updatesource" depends="package">

		<!-- Release string to be written -->
		<loadfile property="release.str"
			srcFile="BuildNo.txt" failonerror="true">
		</loadfile>

		<property environment="env" />

		<packageversion text="${release.str}" start="2" property="release.num" />
		<echo message="Using package version number : ${release.num}"/>
		<replace file="cordova-plugin-unvired-push-sdk/package.json" token="@PACKAGE_NUMBER@" value='${release.num}'/>
		<replace file="cordova-plugin-unvired-push-sdk/plugin.xml" token="@PACKAGE_NUMBER@" value='${release.num}'/>
		<tar destfile="dist/cordova-plugin-unvired-push-sdk.tgz" basedir="cordova-plugin-unvired-push-sdk" longfile="posix" compression="gzip" />
    </target>   
	
    <target name="npmpublish" depends="updatesource" if="${env.PUBLISH}">
		<echo message="Publishing to NPM Registry"/>
		<exec executable="npm"  failonerror="true">
			<arg value="--registry"/>
			<arg value="https://registry.npmjs.org"/>
			<arg value="publish"/>
			<arg value="cordova-plugin-unvired-push-sdk"/>
		</exec>
		
    </target>

	<target name="publish" depends="npmpublish">
		<!-- =================================================================== -->
		<!-- Set Artifactory target depending on release build or nightly        -->
		<!-- =================================================================== -->
		<property environment="env"/>
		<property name="pub.repo" value="client-release"/>
		<property name="pub.repodir" value="unvired-client-release"/>

		<!-- Release string to be written -->
		<loadfile property="release.str"
			srcFile="BuildNo.txt" failonerror="true">
		</loadfile>

		<echo message="Publishing to artifactory repository: ${pub.repodir}"/>
		<ivy:resolve  revision="${release.str}"/>
		<ivy:publish resolver="artifactory-publish" revision="${release.str}" update="true" publishivy="false">
			<artifacts pattern="${dist.dir}/[artifact].[ext]" />
		</ivy:publish>
	
	</target>
</project>