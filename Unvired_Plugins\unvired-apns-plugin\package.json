{"name": "cordova-plugin-unvired-push-sdk", "description": "Register and receive APNS push notifications.", "version": "@PACKAGE_NUMBER@", "repository": {"type": "git", "url": "https://github.com/NeutrinosPlatform/cordova-plugin-unvired-push-sdk"}, "bugs": {"url": "https://github.com/NeutrinosPlatform/cordova-plugin-unvired-push-sdk/issues"}, "cordova": {"id": "cordova-plugin-unvired-push-sdk", "platforms": ["ios"]}, "keywords": ["ecosystem:cordova", "ecosystem:phonegap", "cordova-ios"], "engines": {"cordovaDependencies": {"cordova": ">=7.1.0", "cordova-ios": ">=4.5.0"}}, "author": "jatahwor<PERSON>", "license": "MIT"}